name: Deploy VTJ Docs to GitHub Pages

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

# 设置权限
permissions:
  contents: read
  pages: write
  id-token: write

# 只允许一个并发部署，跳过正在运行和最新队列之间的运行队列
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # 构建作业
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 需要完整历史记录用于lastUpdated

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: latest

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Install dependencies
        run: |
          npm install -g lerna@latest pnpm@latest --registry=https://registry.npmmirror.com
          pnpm install --unsafe-perm --registry=https://registry.npmmirror.com

      - name: Setup Pages
        uses: actions/configure-pages@v4

      - name: Build docs
        run: |
          cd docs
          vitepress build --target es2015
        env:
          NODE_OPTIONS: --max_old_space_size=8192
          BUILD_TYPE: github

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: docs/dist

  # 部署作业
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
