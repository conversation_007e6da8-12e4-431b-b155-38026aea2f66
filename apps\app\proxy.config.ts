export default {
  '/api': {
    target: 'http://localhost:3000',
    changeOrigin: true,
    ws: true
  },
  '/Manage': {
    target: 'http://*************:8000', // 你的实际API服务器地址
    changeOrigin: true,
    ws: true,
    secure: false, // 如果是HTTP而不是HTTPS，设置为false
    rewrite: (path: string) => {
      console.log('代理请求:', path);
      return path;
    }
  },
  // 如果有其他API路径，也可以添加
  '/Account': {
    target: 'http://*************:8000', // 同样更新为实际服务器地址
    changeOrigin: true,
    ws: true,
    secure: false
  }
};
