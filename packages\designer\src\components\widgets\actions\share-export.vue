<template>
  <div class="share-export">
    <ElDialog
      v-model="visible"
      title="分享导出"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="export-options">
        <ElRadioGroup v-model="exportType" class="export-type-group">
          <ElRadio value="html">导出HTML文件</ElRadio>
          <ElRadio value="link">生成分享链接</ElRadio>
          <ElRadio value="qr">生成二维码</ElRadio>
        </ElRadioGroup>

        <div v-if="exportType === 'html'" class="html-export">
          <ElForm :model="htmlOptions" label-width="100px">
            <ElFormItem label="文件名">
              <ElInput v-model="htmlOptions.filename" placeholder="请输入文件名" />
            </ElFormItem>
            <ElFormItem label="包含样式">
              <ElSwitch v-model="htmlOptions.includeStyles" />
            </ElFormItem>
            <ElFormItem label="包含脚本">
              <ElSwitch v-model="htmlOptions.includeScripts" />
            </ElFormItem>
          </ElForm>
        </div>

        <div v-if="exportType === 'link'" class="link-export">
          <ElForm :model="linkOptions" label-width="100px">
            <ElFormItem label="有效期">
              <ElSelect v-model="linkOptions.expiry">
                <ElOption label="1小时" value="1h" />
                <ElOption label="1天" value="1d" />
                <ElOption label="7天" value="7d" />
                <ElOption label="永久" value="never" />
              </ElSelect>
            </ElFormItem>
            <ElFormItem label="访问密码">
              <ElInput v-model="linkOptions.password" placeholder="可选，留空则无密码" />
            </ElFormItem>
          </ElForm>
          
          <div v-if="shareLink" class="share-result">
            <ElInput v-model="shareLink" readonly>
              <template #append>
                <ElButton @click="copyLink">复制</ElButton>
              </template>
            </ElInput>
          </div>
        </div>

        <div v-if="exportType === 'qr'" class="qr-export">
          <div v-if="qrCode" class="qr-code">
            <canvas ref="qrCanvas"></canvas>
            <p>扫描二维码查看页面</p>
          </div>
        </div>
      </div>

      <template #footer>
        <ElButton @click="visible = false">取消</ElButton>
        <ElButton type="primary" @click="handleExport" :loading="exporting">
          {{ exportType === 'html' ? '导出' : '生成' }}
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue';
import { ElDialog, ElRadioGroup, ElRadio, ElForm, ElFormItem, ElInput, ElSwitch, ElSelect, ElOption, ElButton, ElMessage } from 'element-plus';
// import { generator } from '@vtj/coder';
import { type BlockFile, type PageFile } from '@vtj/core';

interface Props {
  visible: boolean;
  currentFile?: BlockFile | PageFile | null;
  materials?: Map<string, any>;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const exportType = ref<'html' | 'link' | 'qr'>('html');
const exporting = ref(false);
const shareLink = ref('');
const qrCode = ref('');
const qrCanvas = ref<HTMLCanvasElement>();

const htmlOptions = reactive({
  filename: '',
  includeStyles: true,
  includeScripts: true
});

const linkOptions = reactive({
  expiry: '7d',
  password: ''
});

// 导出HTML文件
const exportHTML = async () => {
  if (!props.currentFile || !props.materials) {
    ElMessage.error('没有可导出的内容');
    return;
  }

  try {
    exporting.value = true;
    
    // 使用代码生成器生成Vue组件
    // const vueCode = await generator(
    //   props.currentFile,
    //   props.materials,
    //   [],
    //   'web'
    // );
    const vueCode = '<div>临时占位符</div>';

    // 生成完整的HTML文件
    const htmlContent = generateHTMLTemplate(vueCode, htmlOptions);
    
    // 下载文件
    downloadFile(htmlContent, `${htmlOptions.filename || 'vtj-page'}.html`, 'text/html');
    
    ElMessage.success('导出成功');
    visible.value = false;
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  } finally {
    exporting.value = false;
  }
};

// 生成分享链接
const generateShareLink = async () => {
  if (!props.currentFile) {
    ElMessage.error('没有可分享的内容');
    return;
  }

  try {
    exporting.value = true;
    
    // 这里需要调用后端API保存页面数据并生成分享链接
    // 暂时生成一个模拟链接
    const pageId = generateUniqueId();
    const baseUrl = window.location.origin;
    shareLink.value = `${baseUrl}/share/${pageId}`;
    
    ElMessage.success('分享链接生成成功');
  } catch (error) {
    console.error('生成分享链接失败:', error);
    ElMessage.error('生成分享链接失败');
  } finally {
    exporting.value = false;
  }
};

// 生成二维码
const generateQRCode = async () => {
  if (!shareLink.value) {
    await generateShareLink();
  }

  if (shareLink.value && qrCanvas.value) {
    try {
      // 简单的二维码生成，实际项目中可以使用qrcode库
      const ctx = qrCanvas.value.getContext('2d');
      if (ctx) {
        ctx.fillStyle = '#000';
        ctx.fillRect(0, 0, 200, 200);
        ctx.fillStyle = '#fff';
        ctx.font = '12px Arial';
        ctx.fillText('二维码功能', 50, 100);
        ctx.fillText('需要安装qrcode库', 30, 120);
      }
      qrCode.value = shareLink.value;
    } catch (error) {
      console.error('生成二维码失败:', error);
      ElMessage.error('生成二维码失败');
    }
  }
};

// 处理导出
const handleExport = async () => {
  switch (exportType.value) {
    case 'html':
      await exportHTML();
      break;
    case 'link':
      await generateShareLink();
      break;
    case 'qr':
      await generateQRCode();
      break;
  }
};

// 复制链接
const copyLink = async () => {
  try {
    await navigator.clipboard.writeText(shareLink.value);
    ElMessage.success('链接已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

// 生成HTML模板
const generateHTMLTemplate = (vueCode: string, options: typeof htmlOptions) => {
  const stylesLink = options.includeStyles ? '<link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">' : '';

  let html = '<!DOCTYPE html>\n<html lang="zh-CN">\n<head>\n  <meta charset="UTF-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1.0">\n  <title>VTJ页面</title>\n';
  html += stylesLink;
  html += '\n</head>\n<body>\n  <div id="app"></div>\n';

  if (options.includeScripts) {
    const scriptTag = 'script';
    html += '  <' + scriptTag + ' src="https://unpkg.com/vue@3/dist/vue.global.js"></' + scriptTag + '>\n';
    html += '  <' + scriptTag + ' src="https://unpkg.com/element-plus/dist/index.full.js"></' + scriptTag + '>\n';
    html += '  <' + scriptTag + '>\n';
    html += '    const { createApp } = Vue;\n';
    html += '    const app = createApp({\n';
    html += '      template: `' + vueCode.replace(/`/g, '\\`') + '`\n';
    html += '    });\n';
    html += '    app.use(ElementPlus);\n';
    html += '    app.mount(\'#app\');\n';
    html += '  </' + scriptTag + '>\n';
  }

  html += '</body>\n</html>';
  return html;
};

// 下载文件
const downloadFile = (content: string, filename: string, type: string) => {
  const blob = new Blob([content], { type });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 生成唯一ID
const generateUniqueId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

// 监听文件变化，自动设置文件名
watch(() => props.currentFile, (file) => {
  if (file) {
    htmlOptions.filename = file.title || file.name || 'vtj-page';
  }
});
</script>

<style scoped>
.export-options {
  padding: 20px 0;
}

.export-type-group {
  margin-bottom: 20px;
}

.export-type-group .el-radio {
  display: block;
  margin-bottom: 10px;
}

.share-result {
  margin-top: 20px;
}

.qr-code {
  text-align: center;
  padding: 20px;
}

.qr-code canvas {
  margin-bottom: 10px;
}
</style>
